/**
 * Search form component for student dashboard
 */

import React, { useState } from 'react';
import { Search, Users, User } from 'lucide-react';

const SearchForm = ({ 
  programs, 
  semesters, 
  onSearchBelowAverage, 
  onSearchSingleStudent,
  loading 
}) => {
  const [searchType, setSearchType] = useState('belowAverage');
  const [selectedProgram, setSelectedProgram] = useState('');
  const [selectedSemester, setSelectedSemester] = useState('');
  const [regNumber, setRegNumber] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (searchType === 'belowAverage') {
      if (!selectedProgram || !selectedSemester) {
        alert('Please select both program and semester');
        return;
      }
      onSearchBelowAverage(selectedProgram, parseInt(selectedSemester));
    } else {
      if (!regNumber.trim()) {
        alert('Please enter a registration number');
        return;
      }
      onSearchSingleStudent(regNumber.trim());
    }
  };

  const resetForm = () => {
    setSelectedProgram('');
    setSelectedSemester('');
    setRegNumber('');
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
        <Search className="w-6 h-6" />
        Student Search
      </h2>

      <div className="mb-6">
        <div className="flex gap-4 mb-4">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="searchType"
              value="belowAverage"
              checked={searchType === 'belowAverage'}
              onChange={(e) => {
                setSearchType(e.target.value);
                resetForm();
              }}
              className="w-4 h-4 text-blue-600"
            />
            <Users className="w-4 h-4" />
            <span className="text-gray-700">Students Below Average GPA</span>
          </label>
          
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="searchType"
              value="singleStudent"
              checked={searchType === 'singleStudent'}
              onChange={(e) => {
                setSearchType(e.target.value);
                resetForm();
              }}
              className="w-4 h-4 text-blue-600"
            />
            <User className="w-4 h-4" />
            <span className="text-gray-700">Single Student Lookup</span>
          </label>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {searchType === 'belowAverage' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="program" className="block text-sm font-medium text-gray-700 mb-2">
                Select Program
              </label>
              <select
                id="program"
                value={selectedProgram}
                onChange={(e) => setSelectedProgram(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Choose a program...</option>
                {programs.map((program) => (
                  <option key={program.code} value={program.code}>
                    {program.code} - {program.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="semester" className="block text-sm font-medium text-gray-700 mb-2">
                Select Semester
              </label>
              <select
                id="semester"
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Choose a semester...</option>
                {semesters.map((semester) => (
                  <option key={semester} value={semester}>
                    Semester {semester}
                  </option>
                ))}
              </select>
            </div>
          </div>
        ) : (
          <div>
            <label htmlFor="regNumber" className="block text-sm font-medium text-gray-700 mb-2">
              Student Registration Number
            </label>
            <input
              type="text"
              id="regNumber"
              value={regNumber}
              onChange={(e) => setRegNumber(e.target.value)}
              placeholder="Enter registration number (e.g., 240101113)"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>
        )}

        <div className="flex gap-3">
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200 flex items-center gap-2"
          >
            <Search className="w-4 h-4" />
            {loading ? 'Searching...' : 'Search'}
          </button>
          
          <button
            type="button"
            onClick={resetForm}
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200"
          >
            Reset
          </button>
        </div>
      </form>

      {searchType === 'belowAverage' && (
        <div className="mt-4 p-4 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> This search will find all students in the selected program and semester 
            whose GPA is below the program's average GPA (calculated from passing students only).
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchForm;
