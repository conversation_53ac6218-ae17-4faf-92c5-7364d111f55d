/**
 * Student table component for displaying search results
 */

import React from 'react';
import { AlertCircle, CheckCircle, Star, TrendingUp } from 'lucide-react';

const StudentTable = ({ students, title, showProgramStats = false }) => {
  if (!students || students.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-gray-500">No students found matching your criteria.</p>
        </div>
      </div>
    );
  }

  const getRecommendationIcon = (status) => {
    switch (status) {
      case 'proceed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'redirect':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'excellent':
        return <Star className="w-5 h-5 text-blue-600" />;
      default:
        return <TrendingUp className="w-5 h-5 text-gray-600" />;
    }
  };

  const getRecommendationBadge = (recommendation) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    
    switch (recommendation.status) {
      case 'proceed':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'redirect':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'excellent':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  // Get program stats from first student (they should all be the same for below-average searches)
  const programStats = students[0]?.programStats;
  const interval = students[0]?.interval;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">{title}</h3>
      
      {showProgramStats && programStats && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-800 mb-2">Program Statistics</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Students:</span>
              <span className="ml-2 font-medium">{programStats.totalStudents}</span>
            </div>
            <div>
              <span className="text-gray-600">Passing Students:</span>
              <span className="ml-2 font-medium text-green-600">{programStats.passingStudents}</span>
            </div>
            <div>
              <span className="text-gray-600">Failing Students:</span>
              <span className="ml-2 font-medium text-red-600">{programStats.failingStudents}</span>
            </div>
            <div>
              <span className="text-gray-600">Average GPA:</span>
              <span className="ml-2 font-medium text-blue-600">{programStats.averageGPA}</span>
            </div>
          </div>
          {interval && (
            <div className="mt-2 text-sm">
              <span className="text-gray-600">Acceptable Range:</span>
              <span className="ml-2 font-medium">{interval.lowerBound} - {interval.upperBound}</span>
            </div>
          )}
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full table-auto">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Student ID
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Program
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Semester
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Current GPA
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Cumulative GPA
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Recommendation
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {students.map((student, index) => (
              <tr key={student.StudentIdNumber || index} className="hover:bg-gray-50">
                <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {student.StudentIdNumber}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div>
                    <div className="font-medium">{student.ProgrammeCode}</div>
                    <div className="text-xs text-gray-400 truncate max-w-xs">
                      {student.Programme}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                  {student.StudentSemesterOfStudy}
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`font-medium ${
                    student.Sem1SemesterOfStudyGPA >= 2.0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {student.Sem1SemesterOfStudyGPA.toFixed(2)}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`font-medium ${
                    student.CumulativeGPA >= 2.0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {student.CumulativeGPA.toFixed(2)}
                  </span>
                </td>
                <td className="px-4 py-4 whitespace-nowrap">
                  {student.recommendation && (
                    <span className={getRecommendationBadge(student.recommendation)}>
                      {student.recommendation.status}
                    </span>
                  )}
                </td>
                <td className="px-4 py-4 text-sm text-gray-500">
                  {student.recommendation && (
                    <div className="flex items-start gap-2">
                      {getRecommendationIcon(student.recommendation.status)}
                      <div>
                        <div className="font-medium text-gray-900">
                          {student.recommendation.message}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {student.recommendation.action}
                        </div>
                      </div>
                    </div>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-4 text-sm text-gray-600">
        Showing {students.length} student{students.length !== 1 ? 's' : ''}
      </div>
    </div>
  );
};

export default StudentTable;
