/**
 * Data loading utilities for student dashboard
 */

import Papa from 'papaparse';

/**
 * Load and parse student data from CSV file
 * @returns {Promise<Array>} Promise that resolves to array of student objects
 */
export async function loadStudentData() {
  try {
    const response = await fetch('/students.csv');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const csvText = await response.text();
    
    return new Promise((resolve, reject) => {
      Papa.parse(csvText, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
        transform: (value, field) => {
          // Transform specific fields to appropriate types
          if (field === 'YearOfStudy' || field === 'StudentSemesterOfStudy') {
            return parseInt(value) || 0;
          } else if (field === 'Sem1SemesterOfStudyGPA' || field === 'CumulativeGPA') {
            return parseFloat(value) || 0;
          }
          return value.trim();
        },
        complete: (results) => {
          if (results.errors.length > 0) {
            console.warn('CSV parsing warnings:', results.errors);
          }
          resolve(results.data);
        },
        error: (error) => {
          reject(new Error(`CSV parsing error: ${error.message}`));
        }
      });
    });
  } catch (error) {
    console.error('Error loading student data:', error);
    throw new Error(`Failed to load student data: ${error.message}`);
  }
}

/**
 * Validate student data structure
 * @param {Array} students - Array of student objects
 * @returns {Object} Validation result with isValid flag and errors
 */
export function validateStudentData(students) {
  const errors = [];
  const requiredFields = [
    'StudentIdNumber',
    'ProgrammeCode',
    'Programme',
    'YearOfStudy',
    'AcademicSemester',
    'StudentSemesterOfStudy',
    'Sem1SemesterOfStudyGPA',
    'CumulativeGPA'
  ];

  if (!Array.isArray(students)) {
    errors.push('Student data must be an array');
    return { isValid: false, errors };
  }

  if (students.length === 0) {
    errors.push('No student data found');
    return { isValid: false, errors };
  }

  // Check first few records for required fields
  const sampleSize = Math.min(5, students.length);
  for (let i = 0; i < sampleSize; i++) {
    const student = students[i];
    
    requiredFields.forEach(field => {
      if (!(field in student)) {
        errors.push(`Missing required field '${field}' in record ${i + 1}`);
      }
    });

    // Validate GPA values
    if (typeof student.Sem1SemesterOfStudyGPA !== 'number' || 
        student.Sem1SemesterOfStudyGPA < 0 || 
        student.Sem1SemesterOfStudyGPA > 4) {
      errors.push(`Invalid Sem1SemesterOfStudyGPA value in record ${i + 1}: ${student.Sem1SemesterOfStudyGPA}`);
    }

    if (typeof student.CumulativeGPA !== 'number' || 
        student.CumulativeGPA < 0 || 
        student.CumulativeGPA > 4) {
      errors.push(`Invalid CumulativeGPA value in record ${i + 1}: ${student.CumulativeGPA}`);
    }

    // Validate semester values
    if (typeof student.StudentSemesterOfStudy !== 'number' || 
        student.StudentSemesterOfStudy < 1) {
      errors.push(`Invalid StudentSemesterOfStudy value in record ${i + 1}: ${student.StudentSemesterOfStudy}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    totalRecords: students.length,
    validRecords: students.filter(student => 
      requiredFields.every(field => field in student) &&
      typeof student.Sem1SemesterOfStudyGPA === 'number' &&
      typeof student.CumulativeGPA === 'number' &&
      typeof student.StudentSemesterOfStudy === 'number'
    ).length
  };
}

/**
 * Get data summary statistics
 * @param {Array} students - Array of student objects
 * @returns {Object} Summary statistics
 */
export function getDataSummary(students) {
  if (!students || students.length === 0) {
    return {
      totalStudents: 0,
      programs: [],
      semesters: [],
      gpaRange: { min: 0, max: 0 },
      averageGPA: 0
    };
  }

  const programs = [...new Set(students.map(s => s.ProgrammeCode))].sort();
  const semesters = [...new Set(students.map(s => s.StudentSemesterOfStudy))].sort((a, b) => a - b);
  
  const gpas = students.map(s => s.Sem1SemesterOfStudyGPA).filter(gpa => !isNaN(gpa));
  const gpaRange = {
    min: Math.min(...gpas),
    max: Math.max(...gpas)
  };
  
  const averageGPA = gpas.length > 0 ? gpas.reduce((sum, gpa) => sum + gpa, 0) / gpas.length : 0;

  return {
    totalStudents: students.length,
    programs,
    semesters,
    gpaRange,
    averageGPA: parseFloat(averageGPA.toFixed(2))
  };
}
