/**
 * Core GPA calculation utilities for student dashboard
 */

// GPA threshold for passing (typically 2.0 or above)
const PASSING_GPA_THRESHOLD = 2.0;

/**
 * Parse CSV data and convert to student objects
 * @param {string} csvData - Raw CSV data
 * @returns {Array} Array of student objects
 */
export function parseStudentData(csvData) {
  const lines = csvData.trim().split('\n');
  const headers = lines[0].split(',');
  
  return lines.slice(1)
    .filter(line => line.trim()) // Remove empty lines
    .map(line => {
      const values = line.split(',');
      const student = {};
      
      headers.forEach((header, index) => {
        const key = header.trim();
        let value = values[index]?.trim() || '';
        
        // Convert numeric fields
        if (key === 'YearOfStudy' || key === 'StudentSemesterOfStudy') {
          value = parseInt(value) || 0;
        } else if (key === 'Sem1SemesterOfStudyGPA' || key === 'CumulativeGPA') {
          value = parseFloat(value) || 0;
        }
        
        student[key] = value;
      });
      
      return student;
    });
}

/**
 * Calculate average GPA for a program, considering only passing students
 * @param {Array} students - Array of student objects
 * @param {string} programCode - Program code to filter by
 * @param {number} semesterNumber - Semester number to filter by
 * @returns {Object} Object containing average GPA and related statistics
 */
export function calculateProgramAverageGPA(students, programCode, semesterNumber) {
  // Filter students by program and semester
  const programStudents = students.filter(student => 
    student.ProgrammeCode === programCode && 
    student.StudentSemesterOfStudy === semesterNumber
  );

  if (programStudents.length === 0) {
    return {
      averageGPA: 0,
      totalStudents: 0,
      passingStudents: 0,
      failingStudents: 0,
      passingStudentGPAs: [],
      failingStudentGPAs: []
    };
  }

  // Separate passing and failing students
  const passingStudents = programStudents.filter(student => 
    student.Sem1SemesterOfStudyGPA >= PASSING_GPA_THRESHOLD
  );
  
  const failingStudents = programStudents.filter(student => 
    student.Sem1SemesterOfStudyGPA < PASSING_GPA_THRESHOLD
  );

  // Calculate average GPA only for passing students
  const passingGPAs = passingStudents.map(student => student.Sem1SemesterOfStudyGPA);
  const averageGPA = passingGPAs.length > 0 
    ? passingGPAs.reduce((sum, gpa) => sum + gpa, 0) / passingGPAs.length 
    : 0;

  return {
    averageGPA: parseFloat(averageGPA.toFixed(2)),
    totalStudents: programStudents.length,
    passingStudents: passingStudents.length,
    failingStudents: failingStudents.length,
    passingStudentGPAs: passingGPAs,
    failingStudentGPAs: failingStudents.map(student => student.Sem1SemesterOfStudyGPA),
    allStudents: programStudents
  };
}

/**
 * Create GPA interval with 0.5 deduction from average
 * @param {number} averageGPA - Average GPA of the program
 * @returns {Object} Object containing interval bounds and recommendations
 */
export function createGPAInterval(averageGPA) {
  const lowerBound = Math.max(0, averageGPA - 0.5);
  const upperBound = averageGPA;
  
  return {
    lowerBound: parseFloat(lowerBound.toFixed(2)),
    upperBound: parseFloat(upperBound.toFixed(2)),
    averageGPA: parseFloat(averageGPA.toFixed(2))
  };
}

/**
 * Categorize program as qualitative or quantitative based on program code
 * @param {string} programCode - Program code
 * @returns {string} 'quantitative' or 'qualitative'
 */
export function categorizeProgramType(programCode) {
  const quantitativePrograms = [
    'BBST', // Biostatistics
    'BIRE', // Irrigation Engineering
    'BENG', // Engineering programs
    'BMATH', // Mathematics
    'BSTAT', // Statistics
    'BCOMP', // Computer Science
    'BPHYS', // Physics
    'BCHEM' // Chemistry
  ];
  
  const programPrefix = programCode.substring(0, 4);
  return quantitativePrograms.includes(programPrefix) ? 'quantitative' : 'qualitative';
}

/**
 * Generate recommendations based on GPA performance
 * @param {number} studentGPA - Student's GPA
 * @param {Object} interval - GPA interval object
 * @param {string} programType - Program type (quantitative/qualitative)
 * @returns {Object} Recommendation object
 */
export function generateRecommendation(studentGPA, interval, programType) {
  const { lowerBound, upperBound } = interval;
  
  if (studentGPA >= lowerBound && studentGPA <= upperBound) {
    return {
      status: 'proceed',
      message: 'Student can proceed to the next semester',
      action: 'Continue with current program',
      color: 'green',
      details: `GPA ${studentGPA} falls within the acceptable range (${lowerBound} - ${upperBound})`
    };
  } else if (studentGPA < lowerBound) {
    return {
      status: 'redirect',
      message: 'Student needs redirection within categories',
      action: programType === 'quantitative' 
        ? 'Consider switching to a qualitative program or seek additional support'
        : 'Consider switching to a quantitative program or seek additional support',
      color: 'red',
      details: `GPA ${studentGPA} is below the acceptable range (${lowerBound} - ${upperBound})`
    };
  } else {
    return {
      status: 'excellent',
      message: 'Student is performing excellently',
      action: 'Continue with current program and consider advanced opportunities',
      color: 'blue',
      details: `GPA ${studentGPA} exceeds the program average`
    };
  }
}

/**
 * Get students below average GPA for a specific program and semester
 * @param {Array} students - Array of student objects
 * @param {string} programCode - Program code
 * @param {number} semesterNumber - Semester number
 * @returns {Array} Array of students below average with recommendations
 */
export function getStudentsBelowAverage(students, programCode, semesterNumber) {
  const programStats = calculateProgramAverageGPA(students, programCode, semesterNumber);
  
  if (programStats.totalStudents === 0) {
    return [];
  }
  
  const interval = createGPAInterval(programStats.averageGPA);
  const programType = categorizeProgramType(programCode);
  
  return programStats.allStudents
    .filter(student => student.Sem1SemesterOfStudyGPA < programStats.averageGPA)
    .map(student => ({
      ...student,
      recommendation: generateRecommendation(
        student.Sem1SemesterOfStudyGPA, 
        interval, 
        programType
      ),
      programStats,
      interval
    }));
}

/**
 * Find a single student by registration number
 * @param {Array} students - Array of student objects
 * @param {string} regNumber - Student registration number
 * @returns {Object|null} Student object with recommendations or null if not found
 */
export function findStudentByRegNumber(students, regNumber) {
  const student = students.find(s => s.StudentIdNumber === regNumber);
  
  if (!student) {
    return null;
  }
  
  // Get program statistics for this student's program and semester
  const programStats = calculateProgramAverageGPA(
    students, 
    student.ProgrammeCode, 
    student.StudentSemesterOfStudy
  );
  
  const interval = createGPAInterval(programStats.averageGPA);
  const programType = categorizeProgramType(student.ProgrammeCode);
  
  return {
    ...student,
    recommendation: generateRecommendation(
      student.Sem1SemesterOfStudyGPA, 
      interval, 
      programType
    ),
    programStats,
    interval
  };
}

/**
 * Get all unique programs from student data
 * @param {Array} students - Array of student objects
 * @returns {Array} Array of unique program objects
 */
export function getUniquePrograms(students) {
  const programMap = new Map();
  
  students.forEach(student => {
    if (!programMap.has(student.ProgrammeCode)) {
      programMap.set(student.ProgrammeCode, {
        code: student.ProgrammeCode,
        name: student.Programme,
        type: categorizeProgramType(student.ProgrammeCode)
      });
    }
  });
  
  return Array.from(programMap.values()).sort((a, b) => a.code.localeCompare(b.code));
}

/**
 * Get all unique semesters from student data
 * @param {Array} students - Array of student objects
 * @returns {Array} Array of unique semester numbers
 */
export function getUniqueSemesters(students) {
  const semesters = [...new Set(students.map(s => s.StudentSemesterOfStudy))];
  return semesters.filter(s => s > 0).sort((a, b) => a - b);
}
