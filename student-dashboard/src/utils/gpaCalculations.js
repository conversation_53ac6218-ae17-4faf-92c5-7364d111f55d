/**
 * Core GPA calculation utilities for student dashboard
 */

// GPA threshold for passing (typically 2.0 or above)
const PASSING_GPA_THRESHOLD = 2.0;

/**
 * Parse CSV data and convert to student objects
 * @param {string} csvData - Raw CSV data
 * @returns {Array} Array of student objects
 */
export function parseStudentData(csvData) {
  const lines = csvData.trim().split('\n');
  const headers = lines[0].split(',');
  
  return lines.slice(1)
    .filter(line => line.trim()) // Remove empty lines
    .map(line => {
      const values = line.split(',');
      const student = {};
      
      headers.forEach((header, index) => {
        const key = header.trim();
        let value = values[index]?.trim() || '';
        
        // Convert numeric fields
        if (key === 'YearOfStudy' || key === 'StudentSemesterOfStudy') {
          value = parseInt(value) || 0;
        } else if (key === 'Sem1SemesterOfStudyGPA' || key === 'CumulativeGPA') {
          value = parseFloat(value) || 0;
        }
        
        student[key] = value;
      });
      
      return student;
    });
}

/**
 * Calculate average GPA for a program, considering only passing students
 * @param {Array} students - Array of student objects
 * @param {string} programCode - Program code to filter by
 * @param {number} semesterNumber - Semester number to filter by
 * @returns {Object} Object containing average GPA and related statistics
 */
export function calculateProgramAverageGPA(students, programCode, semesterNumber) {
  // Filter students by program and semester
  const programStudents = students.filter(student => 
    student.ProgrammeCode === programCode && 
    student.StudentSemesterOfStudy === semesterNumber
  );

  if (programStudents.length === 0) {
    return {
      averageGPA: 0,
      totalStudents: 0,
      passingStudents: 0,
      failingStudents: 0,
      passingStudentGPAs: [],
      failingStudentGPAs: []
    };
  }

  // Separate passing and failing students
  const passingStudents = programStudents.filter(student => 
    student.Sem1SemesterOfStudyGPA >= PASSING_GPA_THRESHOLD
  );
  
  const failingStudents = programStudents.filter(student => 
    student.Sem1SemesterOfStudyGPA < PASSING_GPA_THRESHOLD
  );

  // Calculate average GPA only for passing students
  const passingGPAs = passingStudents.map(student => student.Sem1SemesterOfStudyGPA);
  const averageGPA = passingGPAs.length > 0 
    ? passingGPAs.reduce((sum, gpa) => sum + gpa, 0) / passingGPAs.length 
    : 0;

  return {
    averageGPA: parseFloat(averageGPA.toFixed(2)),
    totalStudents: programStudents.length,
    passingStudents: passingStudents.length,
    failingStudents: failingStudents.length,
    passingStudentGPAs: passingGPAs,
    failingStudentGPAs: failingStudents.map(student => student.Sem1SemesterOfStudyGPA),
    allStudents: programStudents
  };
}

/**
 * Create GPA interval with 0.5 deduction from average
 * @param {number} averageGPA - Average GPA of the program
 * @returns {Object} Object containing interval bounds and recommendations
 */
export function createGPAInterval(averageGPA) {
  const lowerBound = Math.max(0, averageGPA - 0.5);
  const upperBound = averageGPA;
  
  return {
    lowerBound: parseFloat(lowerBound.toFixed(2)),
    upperBound: parseFloat(upperBound.toFixed(2)),
    averageGPA: parseFloat(averageGPA.toFixed(2))
  };
}

/**
 * Program database with detailed information
 */
const PROGRAM_DATABASE = {
  'BBST': {
    name: 'Bachelor of Science in Biostatistics',
    type: 'quantitative',
    difficulty: 'high',
    mathIntensive: true,
    alternativePrograms: ['BHSC', 'BCOJ'],
    description: 'Statistics and data analysis focused program'
  },
  'BIRE': {
    name: 'Bachelor of Science in Irrigation Engineering',
    type: 'quantitative',
    difficulty: 'very-high',
    mathIntensive: true,
    alternativePrograms: ['BHSC', 'BCOJ'],
    description: 'Engineering program with heavy math and technical requirements'
  },
  'BCOJ': {
    name: 'Bachelor of Science in Communication and Journalism',
    type: 'qualitative',
    difficulty: 'medium',
    mathIntensive: false,
    alternativePrograms: ['BHSC'],
    description: 'Communication, writing, and media focused program'
  },
  'BHSC': {
    name: 'Bachelor of Science in Human Sciences and Community Services',
    type: 'qualitative',
    difficulty: 'medium',
    mathIntensive: false,
    alternativePrograms: ['BCOJ'],
    description: 'Social sciences and community service focused program'
  }
};

/**
 * Categorize program as qualitative or quantitative based on program code
 * @param {string} programCode - Program code
 * @returns {string} 'quantitative' or 'qualitative'
 */
export function categorizeProgramType(programCode) {
  const programPrefix = programCode.substring(0, 4);
  return PROGRAM_DATABASE[programPrefix]?.type || 'qualitative';
}

/**
 * Get program information from database
 * @param {string} programCode - Program code
 * @returns {Object} Program information object
 */
export function getProgramInfo(programCode) {
  const programPrefix = programCode.substring(0, 4);
  return PROGRAM_DATABASE[programPrefix] || {
    name: 'Unknown Program',
    type: 'qualitative',
    difficulty: 'medium',
    mathIntensive: false,
    alternativePrograms: [],
    description: 'Program information not available'
  };
}

/**
 * Generate specific program recommendations based on current program and performance
 * @param {string} currentProgramCode - Current program code
 * @param {number} studentGPA - Student's GPA
 * @param {Array} allStudents - All student data for calculating program averages
 * @returns {Array} Array of recommended programs with reasons
 */
export function generateSpecificProgramRecommendations(currentProgramCode, studentGPA, allStudents) {
  const currentProgram = getProgramInfo(currentProgramCode);
  const recommendations = [];

  // Get alternative programs from current program
  const alternativePrograms = currentProgram.alternativePrograms || [];

  // Calculate average GPAs for alternative programs
  alternativePrograms.forEach(altProgramCode => {
    const altProgramStats = calculateProgramAverageGPA(allStudents, altProgramCode, 1);
    const altProgram = getProgramInfo(altProgramCode);

    if (altProgramStats.totalStudents > 0) {
      const successRate = (altProgramStats.passingStudents / altProgramStats.totalStudents) * 100;
      const avgGPA = altProgramStats.averageGPA;

      // Determine if this program might be better suited
      let suitabilityReason = '';
      let suitabilityScore = 0;

      if (currentProgram.type === 'quantitative' && altProgram.type === 'qualitative') {
        suitabilityReason = 'Less math-intensive, may be better suited for your academic strengths';
        suitabilityScore = studentGPA < 2.0 ? 90 : 70;
      } else if (currentProgram.type === 'qualitative' && altProgram.type === 'quantitative') {
        suitabilityReason = 'More structured and analytical approach';
        suitabilityScore = studentGPA >= 2.0 ? 60 : 40;
      } else {
        suitabilityReason = 'Similar academic approach with different focus area';
        suitabilityScore = 50;
      }

      // Boost score if alternative program has higher success rate
      if (avgGPA > 0 && studentGPA >= (avgGPA - 0.7)) {
        suitabilityScore += 20;
        suitabilityReason += '. Your current GPA suggests good potential for success';
      }

      recommendations.push({
        programCode: altProgramCode,
        programName: altProgram.name,
        averageGPA: avgGPA,
        successRate: Math.round(successRate),
        suitabilityScore,
        reason: suitabilityReason,
        totalStudents: altProgramStats.totalStudents
      });
    }
  });

  // Sort by suitability score
  return recommendations.sort((a, b) => b.suitabilityScore - a.suitabilityScore);
}

/**
 * Generate recommendations based on GPA performance
 * @param {number} studentGPA - Student's GPA
 * @param {Object} interval - GPA interval object
 * @param {string} currentProgramCode - Current program code
 * @param {Array} allStudents - All student data for program recommendations
 * @returns {Object} Recommendation object
 */
export function generateRecommendation(studentGPA, interval, currentProgramCode, allStudents = []) {
  const { lowerBound, upperBound } = interval;
  const currentProgram = getProgramInfo(currentProgramCode);

  if (studentGPA >= lowerBound && studentGPA <= upperBound) {
    return {
      status: 'proceed',
      message: 'Student can proceed to the next semester',
      action: 'Continue with current program',
      color: 'green',
      details: `GPA ${studentGPA} falls within the acceptable range (${lowerBound} - ${upperBound})`
    };
  } else if (studentGPA < lowerBound) {
    // Generate specific program recommendations
    const programRecommendations = generateSpecificProgramRecommendations(
      currentProgramCode,
      studentGPA,
      allStudents
    );

    let specificAction = 'Seek additional academic support';

    if (programRecommendations.length > 0) {
      const topRecommendation = programRecommendations[0];
      specificAction = `Consider switching to ${topRecommendation.programName} (${topRecommendation.programCode}) - ${topRecommendation.reason}. Average GPA: ${topRecommendation.averageGPA}, Success rate: ${topRecommendation.successRate}%`;

      // Add second option if available
      if (programRecommendations.length > 1) {
        const secondOption = programRecommendations[1];
        specificAction += ` | Alternative: ${secondOption.programName} (${secondOption.programCode})`;
      }
    }

    return {
      status: 'redirect',
      message: 'Student needs redirection within categories',
      action: specificAction,
      color: 'red',
      details: `GPA ${studentGPA} is below the acceptable range (${lowerBound} - ${upperBound})`,
      programRecommendations
    };
  } else {
    return {
      status: 'excellent',
      message: 'Student is performing excellently',
      action: 'Continue with current program and consider advanced opportunities',
      color: 'blue',
      details: `GPA ${studentGPA} exceeds the program average`
    };
  }
}

/**
 * Get students below average GPA for a specific program and semester
 * @param {Array} students - Array of student objects
 * @param {string} programCode - Program code
 * @param {number} semesterNumber - Semester number
 * @returns {Array} Array of students below average with recommendations
 */
export function getStudentsBelowAverage(students, programCode, semesterNumber) {
  const programStats = calculateProgramAverageGPA(students, programCode, semesterNumber);

  if (programStats.totalStudents === 0) {
    return [];
  }

  const interval = createGPAInterval(programStats.averageGPA);

  return programStats.allStudents
    .filter(student => student.Sem1SemesterOfStudyGPA < programStats.averageGPA)
    .map(student => ({
      ...student,
      recommendation: generateRecommendation(
        student.Sem1SemesterOfStudyGPA,
        interval,
        student.ProgrammeCode,
        students
      ),
      programStats,
      interval
    }));
}

/**
 * Find a single student by registration number
 * @param {Array} students - Array of student objects
 * @param {string} regNumber - Student registration number
 * @returns {Object|null} Student object with recommendations or null if not found
 */
export function findStudentByRegNumber(students, regNumber) {
  const student = students.find(s => s.StudentIdNumber === regNumber);

  if (!student) {
    return null;
  }

  // Get program statistics for this student's program and semester
  const programStats = calculateProgramAverageGPA(
    students,
    student.ProgrammeCode,
    student.StudentSemesterOfStudy
  );

  const interval = createGPAInterval(programStats.averageGPA);

  return {
    ...student,
    recommendation: generateRecommendation(
      student.Sem1SemesterOfStudyGPA,
      interval,
      student.ProgrammeCode,
      students
    ),
    programStats,
    interval
  };
}

/**
 * Get all unique programs from student data
 * @param {Array} students - Array of student objects
 * @returns {Array} Array of unique program objects
 */
export function getUniquePrograms(students) {
  const programMap = new Map();
  
  students.forEach(student => {
    if (!programMap.has(student.ProgrammeCode)) {
      programMap.set(student.ProgrammeCode, {
        code: student.ProgrammeCode,
        name: student.Programme,
        type: categorizeProgramType(student.ProgrammeCode)
      });
    }
  });
  
  return Array.from(programMap.values()).sort((a, b) => a.code.localeCompare(b.code));
}

/**
 * Get all unique semesters from student data
 * @param {Array} students - Array of student objects
 * @returns {Array} Array of unique semester numbers
 */
export function getUniqueSemesters(students) {
  const semesters = [...new Set(students.map(s => s.StudentSemesterOfStudy))];
  return semesters.filter(s => s > 0).sort((a, b) => a - b);
}
