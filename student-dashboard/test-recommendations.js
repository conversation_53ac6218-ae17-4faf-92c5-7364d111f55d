/**
 * Test script to demonstrate the improved recommendation system
 * Run with: node test-recommendations.js
 */

import { 
  generateRecommendation, 
  generateSpecificProgramRecommendations,
  getProgramInfo,
  createGPAInterval,
  calculateProgramAverageGPA
} from './src/utils/gpaCalculations.js';

// Sample student data for testing
const sampleStudents = [
  // BBST students (quantitative program)
  { StudentIdNumber: '240101113', ProgrammeCode: 'BBST', Programme: 'BACHELOR OF SCIENCE IN BIOSTATISTICS', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 1.49 },
  { StudentIdNumber: '240101096', ProgrammeCode: 'BBST', Programme: 'BACHELOR OF SCIENCE IN BIOSTATISTICS', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 2.71 },
  { StudentIdNumber: '240101104', ProgrammeCode: 'BBST', Programme: 'BACHELOR OF SCIENCE IN BIOSTATISTICS', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 3.74 },
  
  // BCOJ students (qualitative program)
  { StudentIdNumber: '240101085', ProgrammeCode: 'BCOJ', Programme: 'BACHELOR OF SCIENCE IN COMMUNICATION AND JOURNALISM', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 1.96 },
  { StudentIdNumber: '240101057', ProgrammeCode: 'BCOJ', Programme: 'BACHELOR OF SCIENCE IN COMMUNICATION AND JOURNALISM', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 2.96 },
  { StudentIdNumber: '240101062', ProgrammeCode: 'BCOJ', Programme: 'BACHELOR OF SCIENCE IN COMMUNICATION AND JOURNALISM', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 3.02 },
  
  // BHSC students (qualitative program)
  { StudentIdNumber: '240101001', ProgrammeCode: 'BHSC', Programme: 'BACHELOR OF SCIENCE IN HUMAN SCIENCES AND COMMUNITY SERVICES', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 2.5 },
  { StudentIdNumber: '240101002', ProgrammeCode: 'BHSC', Programme: 'BACHELOR OF SCIENCE IN HUMAN SCIENCES AND COMMUNITY SERVICES', StudentSemesterOfStudy: 1, Sem1SemesterOfStudyGPA: 3.1 },
];

console.log('=== IMPROVED STUDENT RECOMMENDATION SYSTEM TEST ===\n');

// Test case 1: Student struggling in quantitative program (BBST)
console.log('1. STUDENT STRUGGLING IN QUANTITATIVE PROGRAM (BBST)');
console.log('Student ID: 240101113, GPA: 1.49, Program: Biostatistics');

const bbstStats = calculateProgramAverageGPA(sampleStudents, 'BBST', 1);
const bbstInterval = createGPAInterval(bbstStats.averageGPA);
const strugglingStudentRec = generateRecommendation(1.49, bbstInterval, 'BBST', sampleStudents);

console.log('OLD SYSTEM WOULD SAY: "Consider switching to a qualitative program or seek additional support"');
console.log('NEW SYSTEM SAYS:');
console.log(`- Status: ${strugglingStudentRec.status}`);
console.log(`- Message: ${strugglingStudentRec.message}`);
console.log(`- Action: ${strugglingStudentRec.action}`);
console.log('');

// Test case 2: Student struggling in qualitative program (BCOJ)
console.log('2. STUDENT STRUGGLING IN QUALITATIVE PROGRAM (BCOJ)');
console.log('Student ID: 240101085, GPA: 1.96, Program: Communication and Journalism');

const bcojStats = calculateProgramAverageGPA(sampleStudents, 'BCOJ', 1);
const bcojInterval = createGPAInterval(bcojStats.averageGPA);
const strugglingQualitativeRec = generateRecommendation(1.96, bcojInterval, 'BCOJ', sampleStudents);

console.log('OLD SYSTEM WOULD SAY: "Consider switching to a quantitative program or seek additional support"');
console.log('NEW SYSTEM SAYS:');
console.log(`- Status: ${strugglingQualitativeRec.status}`);
console.log(`- Message: ${strugglingQualitativeRec.message}`);
console.log(`- Action: ${strugglingQualitativeRec.action}`);
console.log('');

// Test case 3: Show specific program recommendations
console.log('3. DETAILED PROGRAM RECOMMENDATIONS FOR BBST STUDENT');
const specificRecs = generateSpecificProgramRecommendations('BBST', 1.49, sampleStudents);
console.log('Available alternative programs:');
specificRecs.forEach((rec, index) => {
  console.log(`${index + 1}. ${rec.programName} (${rec.programCode})`);
  console.log(`   - Reason: ${rec.reason}`);
  console.log(`   - Average GPA: ${rec.averageGPA}`);
  console.log(`   - Success Rate: ${rec.successRate}%`);
  console.log(`   - Suitability Score: ${rec.suitabilityScore}/100`);
  console.log('');
});

console.log('=== SUMMARY ===');
console.log('✅ The new system provides:');
console.log('   - Specific program recommendations instead of generic advice');
console.log('   - Success rates and average GPAs for alternative programs');
console.log('   - Tailored reasons based on program types and student performance');
console.log('   - Quantitative suitability scoring');
console.log('   - Multiple program options when available');
